using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ITAssetAPI.DTOs;
using ITAssetAPI.Services;
using ITAssetAPI.Models;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Text.Json;

namespace ITAssetAPI.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize]
    public class AssetsController : ControllerBase
    {
        private readonly IAssetService _assetService;
        private readonly IExcelService _excelService;
        private readonly IAsyncLock _asyncLock;

        public AssetsController(IAssetService assetService, IExcelService excelService, IAsyncLock asyncLock)
        {
            _assetService = assetService;
            _excelService = excelService;
            _asyncLock = asyncLock;
        }

        [HttpGet]
        public async Task<ActionResult<AssetListResponseDto>> GetAssets(
            [FromQuery] int page = 1,
            [FromQuery] int limit = 20,
            [FromQuery] string? search = null)
        {
            try
            {
                Console.WriteLine($"=== GET ASSETS CONTROLLER DEBUG ===");
                Console.WriteLine($"Page: {page}, Limit: {limit}");
                Console.WriteLine($"Search: '{search}'");

                // 手动从查询字符串中提取多个相同名称的参数
                var categories = new List<string>();
                var statuses = new List<string>();

                if (Request.Query.ContainsKey("category"))
                {
                    categories.AddRange(Request.Query["category"].Where(c => !string.IsNullOrWhiteSpace(c)));
                }

                if (Request.Query.ContainsKey("status"))
                {
                    statuses.AddRange(Request.Query["status"].Where(s => !string.IsNullOrWhiteSpace(s)));
                }

                Console.WriteLine($"Categories from query: [{string.Join(", ", categories)}]");
                Console.WriteLine($"Statuses from query: [{string.Join(", ", statuses)}]");

                var result = await _assetService.GetAssetsAsync(page, limit, search,
                    categories.Count > 0 ? categories.ToArray() : null,
                    statuses.Count > 0 ? statuses.ToArray() : null);

                Console.WriteLine($"Result count: {result.Assets.Count}");
                Console.WriteLine($"=== GET ASSETS CONTROLLER COMPLETED ===");

                return Ok(result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetAssets: {ex.Message}");
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<AssetDto>> GetAsset(int id)
        {
            try
            {
                var asset = await _assetService.GetAssetByIdAsync(id);
                if (asset == null)
                {
                    return NotFound(new { message = "Asset not found" });
                }
                return Ok(asset);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }

        [HttpGet("next-number/{category}")]
        public async Task<ActionResult<string>> GetNextAssetNumber(string category)
        {
            try
            {
                if (!Enum.TryParse<AssetCategory>(category, true, out var categoryEnum))
                {
                    return BadRequest(new { message = "Invalid category" });
                }

                var nextNumber = await _assetService.GenerateNextAssetNumberAsync(categoryEnum);
                return Ok(new { nextAssetNumber = nextNumber });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }

        [HttpPost]
        public async Task<ActionResult<AssetDto>> CreateAsset([FromBody] CreateAssetDto createAssetDto)
        {
            try
            {
                Console.WriteLine($"=== CREATE ASSET REQUEST ===");
                Console.WriteLine($"Asset Name: {createAssetDto.Name}");
                Console.WriteLine($"Category: {createAssetDto.Category}");
                Console.WriteLine($"Assigned To: {createAssetDto.AssignedTo}");
                Console.WriteLine($"User: {User?.Identity?.Name ?? "Unknown"}");
                Console.WriteLine($"User ID: {User?.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? "Unknown"}");
                
                var result = await _assetService.CreateAssetAsync(createAssetDto);
                
                Console.WriteLine($"Asset created successfully: {result.Name} ({result.AssetNumber})");
                Console.WriteLine($"=== CREATE ASSET COMPLETED ===");
                
                return CreatedAtAction(nameof(GetAsset), new { id = result.Id }, result);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error creating asset: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return BadRequest(new { message = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<ActionResult<AssetDto>> UpdateAsset(int id, [FromBody] UpdateAssetDto updateAssetDto)
        {
            try
            {
                Console.WriteLine($"=== UPDATE ASSET REQUEST ===");
                Console.WriteLine($"Asset ID: {id}");
                Console.WriteLine($"Request Data: {JsonSerializer.Serialize(updateAssetDto)}");
                
                // 使用异步锁避免同一资产的并发更新
                using (await _asyncLock.GetLockByKey($"asset_{id}"))
                {
                    var result = await _assetService.UpdateAssetAsync(id, updateAssetDto);
                    if (result == null)
                    {
                        return NotFound(new { message = "Asset not found" });
                    }
                    
                    Console.WriteLine($"Asset updated successfully: {result.Name} ({result.AssetNumber})");
                    Console.WriteLine($"=== UPDATE ASSET COMPLETED ===");
                    
                    return Ok(result);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error updating asset: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }

        [HttpDelete("{id}")]
        public async Task<ActionResult> DeleteAsset(int id)
        {
            try
            {
                var success = await _assetService.DeleteAssetAsync(id);
                if (!success)
                {
                    return NotFound(new { message = "Asset not found" });
                }
                return NoContent();
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }

        [HttpGet("export")]
        public async Task<ActionResult> ExportAssetsToExcel(
            [FromQuery] string? search = null,
            [FromQuery] string[]? categories = null,
            [FromQuery] string[]? statuses = null)
        {
            try
            {
                // 获取所有符合条件的资产（不分页）
                var allAssets = await _assetService.GetAssetsAsync(1, int.MaxValue, search, categories, statuses);
                
                // 转换为Asset模型列表
                var assets = allAssets.Assets.Select(dto => new Asset
                {
                    Id = dto.Id,
                    Name = dto.Name,
                    AssetNumber = dto.AssetNumber,
                    Category = Enum.Parse<AssetCategory>(dto.Category),
                    Status = Enum.Parse<AssetStatus>(dto.Status),
                    Brand = dto.Brand,
                    Model = dto.Model,
                    SerialNumber = dto.SerialNumber,
                    AssignedTo = dto.AssignedTo,
                    PurchaseDate = dto.PurchaseDate,
                    Value = dto.Value,
                    Vendor = dto.Vendor,
                    Description = dto.Description,
                    Location = dto.Location,
                    LastMaintenanceDate = dto.LastMaintenanceDate,
                    NextMaintenanceDate = dto.NextMaintenanceDate,
                    CreatedAt = dto.CreatedAt,
                    UpdatedAt = dto.UpdatedAt
                }).ToList();

                var excelData = await _excelService.ExportAssetsToExcelAsync(assets);
                
                var fileName = $"资产列表_{DateTime.Now:yyyyMMdd_HHmmss}.xlsx";
                return File(excelData, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", fileName);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "Internal server error", details = ex.Message });
            }
        }
    }
} 